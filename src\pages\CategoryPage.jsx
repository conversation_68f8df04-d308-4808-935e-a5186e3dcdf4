import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  SlidersHorizontal,
  Filter,
  X,
  ChevronDown,
  Search,
  Star,
  Home,
  ChevronRight,
  Grid3X3,
  Grid2X2,
  ChevronUp
} from 'lucide-react';
import { dataService } from '../services/dataService';
import ProductCard from '../components/ProductCard';
import PriceRangeSlider from '../components/PriceRangeSlider';

const categoryTitles = {
  'oversized-tees': 'Oversized Tees',
  't-shirts': 'T-Shirts',
  'hoodies': 'Hoodies',
  'shirts': 'Shirts',
  'capri': 'Capri',
  'baggy-jeans': 'Baggy Jeans',
  'fit-jeans': 'Fit Jeans',
  'jackets': 'Jackets',
  'sweatshirt': 'Sweatshirt',
  'shacket': 'Shacket',
  'oversized-t-shirt': 'Oversized T-Shirt',
  'oversized-shirt': 'Oversized Shirt',
  'full-sleeves-shirt': 'Full Sleeves Shirt',
  'regular-fit-t-shirt': 'Regular Fit T-Shirt',
  'bestsellers': 'Bestsellers',
  'deals': 'Deals'
};

// Price Range Slider Component
// Price Range Slider Component
const LegacyPriceRangeSlider = ({ min = 0, max = 200, values, onChange }) => {
  const [isDragging, setIsDragging] = useState(null);
  const sliderRef = useRef(null);

  const getPercentage = useCallback((value) => {
    return ((value - min) / (max - min)) * 100;
  }, [min, max]);

  const getValueFromPosition = useCallback((clientX) => {
    if (!sliderRef.current) return min;
    
    const rect = sliderRef.current.getBoundingClientRect();
    const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    const value = min + percentage * (max - min);
    
    // Round to nearest integer for cleaner values
    return Math.round(value);
  }, [min, max]);

  const handleMove = useCallback((event) => {
    if (!isDragging || !sliderRef.current) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    const clientX = event.type.includes('mouse') 
      ? event.clientX 
      : event.touches?.[0]?.clientX;
    
    if (clientX === undefined) return;
    
    const newValue = getValueFromPosition(clientX);
    
    if (isDragging === 'min') {
      // Ensure min stays within bounds and doesn't exceed max - 1
      const clampedValue = Math.max(min, Math.min(newValue, values[1] - 1));
      if (clampedValue !== values[0]) {
        onChange([clampedValue, values[1]]);
      }
    } else if (isDragging === 'max') {
      // Ensure max stays within bounds and doesn't go below min + 1
      const clampedValue = Math.min(max, Math.max(newValue, values[0] + 1));
      if (clampedValue !== values[1]) {
        onChange([values[0], clampedValue]);
      }
    }
  }, [isDragging, values, getValueFromPosition, onChange]);

  const handleEnd = useCallback(() => {
    setIsDragging(null);
  }, []);

  const handleStart = useCallback((event, thumb) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(thumb);
  }, []);

  useEffect(() => {
    if (!isDragging) return;

    const handleMouseMove = (e) => handleMove(e);
    const handleTouchMove = (e) => handleMove(e);
    const handleMouseUp = () => handleEnd();
    const handleTouchEnd = () => handleEnd();

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('touchend', handleTouchEnd);
    document.addEventListener('touchcancel', handleTouchEnd);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchend', handleTouchEnd);
      document.removeEventListener('touchcancel', handleTouchEnd);
    };
  }, [isDragging, handleMove, handleEnd]);

  const minPercentage = getPercentage(values[0]);
  const maxPercentage = getPercentage(values[1]);

  return (
    <div className="px-4 py-6">
      <div className="relative">
        {/* Track */}
        <div 
          ref={sliderRef}
          className="relative h-2 bg-gray-700 rounded-full cursor-pointer touch-none"
          onTouchStart={(e) => {
            // Handle touch start on track to move the nearest thumb
            if (!sliderRef.current) return;
            const rect = sliderRef.current.getBoundingClientRect();
            const clickX = e.touches[0].clientX;
            const percentage = (clickX - rect.left) / rect.width;
            const value = min + percentage * (max - min);
            const [minVal, maxVal] = values;
            
            // Move the nearest thumb
            if (Math.abs(value - minVal) < Math.abs(value - maxVal)) {
              handleStart(e, 'min');
            } else {
              handleStart(e, 'max');
            }
          }}
        >
          {/* Active range */}
          <div
            className="absolute h-2 bg-gradient-to-r from-[#FF8800] to-[#FF5500] rounded-full transition-all duration-150"
            style={{
              left: `${minPercentage}%`,
              width: `${maxPercentage - minPercentage}%`
            }}
          />
          
          {/* Min thumb */}
          <div
            className={`absolute w-5 h-5 bg-white border-2 border-blue-400 rounded-full cursor-grab shadow-lg transition-all duration-150 ${
              isDragging === 'min' ? 'scale-110 cursor-grabbing shadow-xl' : 'hover:scale-105'
            }`}
            style={{ 
              left: `${minPercentage}%`,
              top: '50%',
              transform: 'translate(-50%, -50%)'
            }}
            onMouseDown={(e) => handleStart(e, 'min')}
            onTouchStart={(e) => handleStart(e, 'min')}
          />
          
          {/* Max thumb */}
          <div
            className={`absolute w-5 h-5 bg-white border-2 border-purple-400 rounded-full cursor-grab shadow-lg transition-all duration-150 ${
              isDragging === 'max' ? 'scale-110 cursor-grabbing shadow-xl' : 'hover:scale-105'
            }`}
            style={{ 
              left: `${maxPercentage}%`,
              top: '50%',
              transform: 'translate(-50%, -50%)'
            }}
            onMouseDown={(e) => handleStart(e, 'max')}
            onTouchStart={(e) => handleStart(e, 'max')}
          />
        </div>
        
        {/* Value display */}
        <div className="flex justify-between mt-4 text-sm text-gray-300 font-medium">
          <span className="px-2 py-1 bg-gray-800 rounded">${values[0]}</span>
          <span className="px-2 py-1 bg-gray-800 rounded">${values[1]}</span>
        </div>
      </div>
    </div>
  );
};

// Demo component
const SliderDemo = () => {
  const [filters, setFilters] = useState({
    priceRange: [0, 200] // Updated default range to 0-200
  });

  return (
    <div className="min-h-screen bg-black p-8">
      <div className="max-w-md mx-auto bg-gray-900 rounded-lg p-6">
        <h3 className="text-white text-lg font-semibold mb-4">Price Range</h3>
        <PriceRangeSlider
          min={0}
          max={200}
          values={filters.priceRange}
          onChange={(newValues) => setFilters(prev => ({
            ...prev,
            priceRange: newValues
          }))}
        />
        <div className="mt-4 text-gray-400 text-sm text-center">
          Selected Range: ${filters.priceRange[0]} - ${filters.priceRange[1]}
        </div>
      </div>
    </div>
  );
};

export default function CategoryPage() {
  const { category } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [allProducts, setAllProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState('newest');
  const [mobileViewMode, setMobileViewMode] = useState('double');
  const [showMobileFilters, setShowMobileFilters] = useState(false);
    // Filter states
  const [filters, setFilters] = useState({
    priceRange: [0, 0],
    sizes: [],
    colors: [],
    rating: 0,
    inStock: false,
    onSale: false
  });

  const productsPerPage = 12;

  // Dynamic price bounds based on loaded products
  const priceBounds = useMemo(() => {
    const prices = allProducts.map(p => (p.is_sale === 1 ? p.sale_price : p.price)).filter(Boolean);
    if (prices.length === 0) {
      return { min: 0, max: 1 };
    }
    const min = Math.floor(Math.min(...prices));
    let max = Math.ceil(Math.max(...prices));
    if (max <= min) {
      max = min + 1; // guarantee valid range for slider
    }
    return { min, max };
  }, [allProducts]);

  // Available filter options
  const availableSizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];
  const availableColors = [
    { name: 'Black', value: 'black', hex: '#000000' },
    { name: 'White', value: 'white', hex: '#FFFFFF' },
    { name: 'Gray', value: 'gray', hex: '#6B7280' },
    { name: 'Navy', value: 'navy', hex: '#1E3A8A' },
    { name: 'Blue', value: 'blue', hex: '#3B82F6' },
    { name: 'Red', value: 'red', hex: '#EF4444' },
    { name: 'Green', value: 'green', hex: '#10B981' },
    { name: 'Yellow', value: 'yellow', hex: '#F59E0B' },
    { name: 'Purple', value: 'purple', hex: '#8B5CF6' },
    { name: 'Pink', value: 'pink', hex: '#EC4899' }
  ];

  // Calculate available ratings with counts
  const availableRatings = useMemo(() => {
    const ratingCounts = [4, 3, 2, 1].map(rating => ({
      rating,
      count: allProducts.filter(product => (product.rating || product.average_rating || 0) >= rating).length
    })).filter(item => item.count > 0);

    return ratingCounts;
  }, [allProducts]);

  // Load products
  useEffect(() => {
    const loadCategoryProducts = async () => {
      setIsLoading(true);
      try {
        let products = [];
        const categoryMap = {
          'hoodies': 'Hoodies',
          't-shirts': 'T-Shirts',
          'shirts': 'Shirts',
          'capri': 'Capri',
          'baggy-jeans': 'Baggy Jeans',
          'fit-jeans': 'Fit Jeans',
          'jackets': 'Jackets',
          'sweatshirt': 'Sweatshirt',
          'shacket': 'Shacket',
          'oversized-t-shirt': 'Oversized T-Shirt',
          'oversized-shirt': 'Oversized Shirt',
          'full-sleeves-shirt': 'Full Sleeves Shirt',
          'regular-fit-t-shirt': 'Regular Fit T-Shirt',
          'oversized-tees': 'Oversized Tees'
        };

        if (category === 'bestsellers') {
          products = await dataService.getBestsellerProducts(100);
        } else if (category === 'deals') {
          const saleProducts = await dataService.getSaleProducts({}, 1, 100);
          products = saleProducts.products;
        } else {
          const targetCategory = categoryMap[category];
          if (targetCategory) {
            const categoryProducts = await dataService.getProductsByCategory(targetCategory, {}, 1, 100);
            products = categoryProducts.products;
          } else {
            products = [];
          }
        }

        if (products && products.length > 0) {
          products = products.map(product => {
            if (product.sale_price && product.sale_price > 0 && product.is_sale !== 1) {
              return { ...product, is_sale: 1 };
            }
            return product;
          });
        }

        setAllProducts(products);
        
        // Set initial price range based on products
        if (products.length > 0) {
          const prices = products.map(p => p.is_sale === 1 ? p.sale_price : p.price).filter(Boolean);
          const minPrice = Math.min(...prices);
          const maxPrice = Math.max(...prices);
          setFilters(prev => ({
            ...prev,
            priceRange: [Math.floor(minPrice), Math.ceil(maxPrice)]
          }));
        }
        
      } catch (error) {
        console.error('Failed to load category products:', error);
        setAllProducts([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadCategoryProducts();
  }, [category]);

  // Apply filters
  useEffect(() => {
    let filtered = [...allProducts];

    // Price filter
    filtered = filtered.filter(product => {
      const price = product.is_sale === 1 ? product.sale_price : product.price;
      return price >= filters.priceRange[0] && price <= filters.priceRange[1];
    });

    // Size filter
    if (filters.sizes.length > 0) {
      filtered = filtered.filter(product => {
        return product.sizes && product.sizes.some(size => filters.sizes.includes(size));
      });
    }

    // Color filter
    if (filters.colors.length > 0) {
      filtered = filtered.filter(product => {
        return product.colors && product.colors.some(color => 
          filters.colors.some(filterColor => 
            color && typeof color === 'object' && color.name && 
            color.name.toLowerCase().includes(filterColor.toLowerCase())
          )
        );
      });
    }

    // Rating filter
    if (filters.rating > 0) {
      filtered = filtered.filter(product => (product.rating || product.average_rating || 0) >= filters.rating);
    }

    // In stock filter
    if (filters.inStock) {
      filtered = filtered.filter(product => product.stock > 0);
    }

    // On sale filter
    if (filters.onSale) {
      filtered = filtered.filter(product => product.is_sale === 1);
    }

    setFilteredProducts(filtered);
    setCurrentPage(1);
  }, [allProducts, filters]);

  // Sorting function
  const sortProducts = useCallback((products, sortMethod) => {
    const sortedProducts = [...products];
    
    switch (sortMethod) {
      case 'price-low':
        sortedProducts.sort((a, b) => {
          const priceA = a.is_sale === 1 ? a.sale_price : a.price || 0;
          const priceB = b.is_sale === 1 ? b.sale_price : b.price || 0;
          return priceA - priceB;
        });
        break;
        
      case 'price-high':
        sortedProducts.sort((a, b) => {
          const priceA = a.is_sale === 1 ? a.sale_price : a.price || 0;
          const priceB = b.is_sale === 1 ? b.sale_price : b.price || 0;
          return priceB - priceA;
        });
        break;
        
      case 'rating':
        sortedProducts.sort((a, b) => (b.rating || b.average_rating || 0) - (a.rating || a.average_rating || 0));
        break;
        
      case 'name':
        sortedProducts.sort((a, b) => a.name.localeCompare(b.name));
        break;
        
      default: // newest
        sortedProducts.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    }
    
    return sortedProducts;
  }, []);

  // Process products with sorting and pagination
  const processedProducts = useMemo(() => {
    return sortProducts(filteredProducts, sortBy);
  }, [filteredProducts, sortBy, sortProducts]);

  // Pagination calculations
  const totalPages = Math.ceil(processedProducts.length / productsPerPage);
  const startIndex = (currentPage - 1) * productsPerPage;
  const currentProducts = processedProducts.slice(startIndex, startIndex + productsPerPage);

  // Filter handlers
  const handleFilterChange = (filterType, value) => {
    setFilters(prev => {
      switch (filterType) {
        case 'priceRange':
          return { ...prev, priceRange: value };
        case 'sizes':
          const newSizes = prev.sizes.includes(value)
            ? prev.sizes.filter(s => s !== value)
            : [...prev.sizes, value];
          return { ...prev, sizes: newSizes };
        case 'colors':
          const newColors = prev.colors.includes(value)
            ? prev.colors.filter(c => c !== value)
            : [...prev.colors, value];
          return { ...prev, colors: newColors };
        case 'rating':
          return { ...prev, rating: prev.rating === value ? 0 : value };
        case 'inStock':
          return { ...prev, inStock: !prev.inStock };
        case 'onSale':
          return { ...prev, onSale: !prev.onSale };
        default:
          return prev;
      }
    });
  };

  const clearAllFilters = () => {
    const prices = allProducts.map(p => p.is_sale === 1 ? p.sale_price : p.price).filter(Boolean);
    const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
    const maxPrice = prices.length > 0 ? Math.max(...prices) : 5000;
    
    setFilters({
      priceRange: [Math.floor(minPrice), Math.ceil(maxPrice)],
      sizes: [],
      colors: [],
      rating: 0,
      inStock: false,
      onSale: false
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.sizes.length > 0) count++;
    if (filters.colors.length > 0) count++;
    if (filters.rating > 0) count++;
    if (filters.inStock) count++;
    if (filters.onSale) count++;
    return count;
  };

  // Filters Component
  const FiltersPanel = ({ isMobile = false }) => (
    <div className="space-y-6">
      {/* Filter Header */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-white/10">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <SlidersHorizontal size={20} className="mr-2" />
          Filters
        </h3>
        {getActiveFiltersCount() > 0 && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
          >
            Clear All
          </button>
        )}
      </div>

      <div className="px-6 space-y-6">
        {/* Sort By */}
        <div className="space-y-3">
          <h4 className="font-medium text-white">Sort By</h4>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="w-full appearance-none bg-black border border-slate-800 text-white px-4 py-2 pr-10 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 hover:bg-slate-900 cursor-pointer transition-all duration-200 font-medium"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
              backgroundPosition: 'right 0.75rem center',
              backgroundRepeat: 'no-repeat',
              backgroundSize:'1.5rem 1.5rem'
            }}
          >
            <option value="newest">Newest First</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="rating">Highest Rated</option>
            <option value="name">Name A-Z</option>
          </select>
        </div>
        
        {/* Price Range */}
        <div className="space-y-3">
          <h4 className="font-medium text-white">Price Range</h4>
          <PriceRangeSlider
            min={priceBounds.min}
            max={priceBounds.max}
            values={
              filters.priceRange[0] === filters.priceRange[1] && priceBounds.max > priceBounds.min
                ? [priceBounds.min, priceBounds.max]
                : [
                    Math.max(priceBounds.min, filters.priceRange[0]),
                    Math.min(priceBounds.max, filters.priceRange[1])
                  ]
            }
            onChange={(values) => handleFilterChange('priceRange', values)}
          />
        </div>

        {/* Sizes */}
        <div className="space-y-3">
          <h4 className="font-medium text-white">Sizes</h4>
          <div className="grid grid-cols-4 gap-2">
            {availableSizes.map(size => (
              <button
                key={size}
                onClick={() => handleFilterChange('sizes', size)}
                className={`p-2 text-sm border rounded-lg transition-all ${
                  filters.sizes.includes(size)
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-white/10 text-gray-300 border-white/20 hover:border-white/40 hover:bg-white/20'
                }`}
              >
                {size}
              </button>
            ))}
          </div>
        </div>

        {/* Colors */}
        <div className="space-y-3">
          <h4 className="font-medium text-white">Colors</h4>
          <div className="grid grid-cols-5 gap-2">
            {availableColors.map(color => (
              <button
                key={color.value}
                onClick={() => handleFilterChange('colors', color.value)}
                className={`relative w-8 h-8 rounded-full border-2 transition-all ${
                  filters.colors.includes(color.value)
                    ? 'border-blue-400 ring-2 ring-blue-400 ring-offset-2 ring-offset-black'
                    : 'border-white/20 hover:border-white/40'
                }`}
                style={{ backgroundColor: color.hex }}
                title={color.name}
              >
                {color.value === 'white' && (
                  <div className="absolute inset-1 border border-gray-300 rounded-full" />
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Rating */}
        <div className="space-y-3">
          <h4 className="font-medium text-white">Minimum Rating</h4>
          <div className="space-y-2">
            {availableRatings.map(({ rating, count }) => (
              <button
                key={rating}
                onClick={() => handleFilterChange('rating', rating)}
                className={`flex items-center justify-between w-full p-2 rounded-lg transition-all ${
                  filters.rating === rating
                    ? 'bg-blue-500/20 text-blue-400'
                    : 'text-gray-300 hover:bg-white/10'
                }`}
              >
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      size={16}
                      className={`${
                        i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'
                      }`}
                    />
                  ))}
                  <span className="ml-2 text-sm">& Up</span>
                </div>
                <span className="text-xs text-gray-400">({count})</span>
              </button>
            ))}
          </div>
        </div>

        {/* Availability */}
        <div className="space-y-3">
          <h4 className="font-medium text-white">Availability</h4>
          <div className="space-y-2">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={filters.inStock}
                onChange={() => handleFilterChange('inStock')}
                className="sr-only"
              />
              <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                filters.inStock ? 'bg-blue-500 border-blue-500' : 'border-white/20'
              }`}>
                {filters.inStock && <div className="w-2 h-2 bg-white rounded-sm" />}
              </div>
              <span className="ml-2 text-sm text-gray-300">In Stock Only</span>
            </label>
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={filters.onSale}
                onChange={() => handleFilterChange('onSale')}
                className="sr-only"
              />
              <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                filters.onSale ? 'bg-blue-500 border-blue-500' : 'border-white/20'
              }`}>
                {filters.onSale && <div className="w-2 h-2 bg-white rounded-sm" />}
              </div>
              <span className="ml-2 text-sm text-gray-300">On Sale</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-black relative overflow-hidden w-full">
      <div className="relative z-10">
        <div className="flex min-h-screen">
          {/* Desktop Sidebar Filters - Always visible */}
          <div className="hidden lg:block w-80 fixed left-0 top-0 h-full z-30">
            <div className="h-full bg-black/40 backdrop-blur-md border-r border-white/10">
              <div className="pt-20 h-full overflow-y-auto">
                <FiltersPanel />
              </div>
            </div>
          </div>

          {/* Mobile Filter Modal */}
          <AnimatePresence>
            {showMobileFilters && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
                onClick={() => setShowMobileFilters(false)}
              >
                <motion.div
                  initial={{ x: -300 }}
                  animate={{ x: 0 }}
                  exit={{ x: -300 }}
                  transition={{ duration: 0.3 }}
                  className="w-80 h-full bg-black/90 backdrop-blur-md border-r border-white/10 overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="flex items-center justify-between p-4 border-b border-white/10">
                    <h3 className="text-lg font-semibold text-white">Filters</h3>
                    <button
                      onClick={() => setShowMobileFilters(false)}
                      className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                    >
                      <X size={20} className="text-gray-400" />
                    </button>
                  </div>
                  <FiltersPanel isMobile />
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Content */}
          <div className="flex-1 pt-4 pb-6 w-full max-w-full transition-all duration-300 lg:ml-80">
            <div className="container mx-auto px-3 md:px-6 lg:px-8 max-w-7xl">
              {/* Navigation & Header Section */}
              {category && (
                <div className="mb-6">
                  {/* Breadcrumb Navigation */}
                  <motion.nav
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mb-8"
                  >
                    <div className="flex items-center space-x-2 text-sm">
                      <Link
                        to="/"
                        className="flex items-center text-slate-400 hover:text-cyan-400 transition-colors font-medium"
                      >
                        <Home size={16} className="mr-1" />
                        Home
                      </Link>
                      <ChevronRight size={14} className="text-slate-600" />
                      <Link
                        to="/collections"
                        className="text-slate-400 hover:text-cyan-400 transition-colors font-medium"
                      >
                        Collections
                      </Link>
                      <ChevronRight size={14} className="text-slate-600" />
                      <span className="text-white font-semibold">
                        {categoryTitles[category]}
                      </span>
                    </div>
                  </motion.nav>

                  {/* Mobile View Options & Filters */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="flex items-center justify-between mb-4"
                  >                    <div className="flex items-center gap-2 lg:hidden">
                      <button
                        onClick={() => setMobileViewMode('single')}
                        className={`p-2.5 rounded-lg transition-all duration-200 ${
                          mobileViewMode === 'single'
                            ? 'bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white border border-orange-500/50 shadow-lg'
                            : 'text-slate-400 hover:text-white hover:bg-slate-800/60'
                        }`}
                        title="Single column view"
                      >
                        <Grid3X3 size={18} />
                      </button>
                      <button
                        onClick={() => setMobileViewMode('double')}
                        className={`p-2.5 rounded-lg transition-all duration-200 ${
                          mobileViewMode === 'double'
                            ? 'bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white border border-orange-500/50 shadow-lg'
                            : 'text-slate-400 hover:text-white hover:bg-slate-800/60'
                        }`}
                        title="Two columns view"
                      >
                        <Grid2X2 size={18} />
                      </button>
                    </div>

                    <div className="flex items-center gap-2">
                      {/* Mobile Filter Button */}
                      <button
                        onClick={() => setShowMobileFilters(true)}
                        className="lg:hidden relative p-2.5 rounded-lg bg-slate-800/60 hover:bg-slate-700/60 text-slate-400 hover:text-white transition-all duration-200"
                      >
                        <Filter size={18} />
                        {getActiveFiltersCount() > 0 && (
                          <span className="absolute -top-1 -right-1 w-5 h-5 bg-cyan-500 text-white text-xs rounded-full flex items-center justify-center">
                            {getActiveFiltersCount()}
                          </span>
                        )}
                      </button>
                    </div>
                  </motion.div>

                  {/* Active Filters Display */}
                  {getActiveFiltersCount() > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-4 flex flex-wrap gap-2"
                    >
                      {filters.sizes.map(size => (
                        <span
                          key={`size-${size}`}
                          className="inline-flex items-center px-3 py-1 bg-cyan-500/20 text-cyan-400 text-sm rounded-full border border-cyan-500/30"
                        >
                          Size: {size}
                          <button
                            onClick={() => handleFilterChange('sizes', size)}
                            className="ml-2 hover:text-cyan-300"
                          >
                            <X size={14} />
                          </button>
                        </span>
                      ))}
                      {filters.colors.map(color => (
                        <span
                          key={`color-${color}`}
                          className="inline-flex items-center px-3 py-1 bg-cyan-500/20 text-cyan-400 text-sm rounded-full border border-cyan-500/30"
                        >
                          Color: {color}
                          <button
                            onClick={() => handleFilterChange('colors', color)}
                            className="ml-2 hover:text-cyan-300"
                          >
                            <X size={14} />
                          </button>
                        </span>
                      ))}
                      {filters.rating > 0 && (
                        <span className="inline-flex items-center px-3 py-1 bg-cyan-500/20 text-cyan-400 text-sm rounded-full border border-cyan-500/30">
                          Rating: {filters.rating}+ stars
                          <button
                            onClick={() => handleFilterChange('rating', filters.rating)}
                            className="ml-2 hover:text-cyan-300"
                          >
                            <X size={14} />
                          </button>
                        </span>
                      )}
                      {filters.inStock && (
                        <span className="inline-flex items-center px-3 py-1 bg-cyan-500/20 text-cyan-400 text-sm rounded-full border border-cyan-500/30">
                          In Stock Only
                          <button
                            onClick={() => handleFilterChange('inStock')}
                            className="ml-2 hover:text-cyan-300"
                          >
                            <X size={14} />
                          </button>
                        </span>
                      )}
                      {filters.onSale && (
                        <span className="inline-flex items-center px-3 py-1 bg-cyan-500/20 text-cyan-400 text-sm rounded-full border border-cyan-500/30">
                          On Sale
                          <button
                            onClick={() => handleFilterChange('onSale')}
                            className="ml-2 hover:text-cyan-300"
                          >
                            <X size={14} />
                          </button>
                        </span>
                      )}
                    </motion.div>
                  )}

                  {/* Category Title */}
                  {/*<motion.div*/}
                  {/*  initial={{ opacity: 0, y: 20 }}*/}
                  {/*  animate={{ opacity: 1, y: 0 }}*/}
                  {/*  transition={{ delay: 0.2 }}*/}
                  {/*  className="mb-8"*/}
                  {/*>*/}
                  {/*  <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">*/}
                  {/*    {categoryTitles[category]}*/}
                  {/*  </h1>*/}
                  {/*  <p className="text-slate-400">*/}
                  {/*    {filteredProducts.length} products available*/}
                  {/*  </p>*/}
                  {/*</motion.div>*/}
                </div>
              )}

              {/* Products Grid */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="relative"
              >
                {isLoading ? (
                  // Loading Skeleton
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
                    {[...Array(8)].map((_, index) => (
                      <div key={index} className="bg-slate-800/40 rounded-xl p-4 animate-pulse">
                        <div className="aspect-[3/4] bg-slate-700/50 rounded-lg mb-4"></div>
                        <div className="h-4 bg-slate-700/50 rounded w-3/4 mb-2"></div>
                        <div className="h-4 bg-slate-700/50 rounded w-1/2"></div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <>
                    {filteredProducts.length === 0 ? (
                      // No Products Found
                      <div className="text-center py-16">
                        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-slate-800/60 mb-4">
                          <Search size={24} className="text-slate-400" />
                        </div>
                        <h3 className="text-xl font-semibold text-white mb-2">No products found</h3>
                        <p className="text-slate-400 mb-6">
                          Try adjusting your filters or search criteria
                        </p>
                        <button
                          onClick={clearAllFilters}
                          className="px-6 py-2 bg-orange-400 text-white rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-200 font-medium"
                        >
                          Clear All Filters
                        </button>
                      </div>
                    ) : (
                      // Products Grid
                      <div>
                        <div className={`grid ${
                          mobileViewMode === 'single' 
                            ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4' 
                            : 'grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4'
                        } gap-4 md:gap-6`}>
                          {currentProducts.map(product => (
                            <ProductCard key={product.id} product={product} />
                          ))}
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.4 }}
                            className="flex justify-center items-center mt-12 gap-2"
                          >
                            <button
                              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                              disabled={currentPage === 1}
                              className="px-4 py-2 bg-slate-800/60 hover:bg-slate-700/60 disabled:bg-slate-800/30 disabled:text-slate-500 text-white rounded-lg transition-all duration-200 disabled:cursor-not-allowed text-sm font-medium"
                            >
                              <span className="hidden sm:inline">Previous</span>
                              <span className="sm:hidden">Prev</span>
                            </button>

                            <div className="flex items-center gap-1">
                              {[...Array(totalPages)].map((_, i) => {
                                const pageNumber = i + 1;
                                const isCurrentPage = pageNumber === currentPage;
                                
                                if (
                                  pageNumber === 1 ||
                                  pageNumber === totalPages ||
                                  (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                                ) {       
                                  return (
                                    <button
                                      key={pageNumber}    
                                      onClick={() => setCurrentPage(pageNumber)}
                                      className={`px-3 py-2 rounded-lg transition-all duration-200 text-sm font-medium ${
                                        isCurrentPage     
                                          ? 'bg-orange-400 text-white shadow-lg'     
                                          : 'bg-slate-800/60 hover:bg-slate-700/60 text-slate-300 hover:text-white'
                                      }`}
                                    >
                                      {pageNumber}        
                                    </button>
                                  );
                                } else if (
                                  pageNumber === currentPage - 2 ||
                                  pageNumber === currentPage + 2
                                ) {
                                  return (
                                    <span key={pageNumber} className="px-2 py-2 text-slate-500">
                                      ...
                                    </span>
                                  );
                                }
                                return null;
                              })}
                            </div>

                            <button
                              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                              disabled={currentPage === totalPages}
                              className="px-4 py-2 bg-slate-800/60 hover:bg-slate-700/60 disabled:bg-slate-800/30 disabled:text-slate-500 text-white rounded-lg transition-all duration-200 disabled:cursor-not-allowed text-sm font-medium"
                            >
                              <span className="hidden sm:inline">Next</span>
                              <span className="sm:hidden">Next</span>
                            </button>
                          </motion.div>
                        )}
                      </div>
                    )}
                  </>
                )}
              </motion.div>
            </div>
          </div>
        </div>
      </div>    </div>
  );
}